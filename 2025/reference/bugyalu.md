由于 platform FE 的业务与别的业务团队有一定区别，为了减少歧义，先定义属于 platform FE 的“产品”、“业务”、“模块”等概念。
- 产品：好的 platform 团队应当可以被看作一个公司，对外提供的产品是“DevSecOps 平台化建设”，目标是“降低研发成本、提升交付效率和安全合规能力，增加客户能产生的价值”。
- 业务：产品之下，有明确客户群体、能独立跑通价值链（设计、生产、销售、交付和售后组成的链条）的单元。整理 platform 的几个方向可以得出如下业务：
业务
客户群体
价值链
业界参考
设计系统
设计师 + 前端
设计需求收集 -> 开发 -> 推广 -> 质量保障/文档建设/问题修复
Ant Design
研发规范/体验
研发
问题/需求收集 -> 工具开发 -> 推动用户接受工具 -> 疑难解答/工具优化
SonarQube
质量体系
QA + 研发
质量需求收集 -> 平台/工具/流程开发 -> 覆盖率提升 -> 疑难解答
质量中台
MIS
市场、运营、业务
同一般的业务项目
/
App
RN 研发
需求收集/指标定义 -> SDK 开发/指标收集 -> 业务接入 SDK -> 疑难解答
/
CI/CD
研发
流水线需求收集 -> 开发 -> 业务接入 -> 疑难解答/问题修复
GitHub Actions + ArgoCD
用户体验指标监控与优化
前端、产品、运营
指标定义 -> 开发 -> 推动用户接受指标/帮助用户优化指标 -> 疑难解答/指标设计优化
Datadog RUM
- 模块/子产品：一个业务可能由一个或多个子产品支撑，如设计系统包含了组件库、图标自助发布工具，质量体系包含了测试指引、用例管理/执行平台、监控模块。这些模块有大有小。
接下来以 Bug 的成果为例，先说明一个成果的 scope（简单模块？复杂模块？完整业务？），再阐述成果本身带来的变化，最后是如何达到该成果。
1. JS Bundle 技术优化
- Scope：App 业务下的性能优化模块；仅遵循业界方案无法达成目标，需要了解 HBS、gzip、AWS 等知识自行开发，不过最终代码复杂度不高。综合考虑下，属于 2-1 的能力范围。
- Changes：首屏加载时间减少 10%、用户每次热更新流量 18MB->10MB。
- Solution：HBS 路由懒加载、手动 gzip 压缩、修改 AWS header 以做到自动解压缩。
- Evidence：B App JS bundle 优化技术文档、B App 路由懒加载技术方案
2. 前端 Lint 能力建设、前端许可证和依赖安全问题
- Scope：研发规范/体验业务下的 lint 模块；由于存在基于业务定制的规则，需要基于 typescript-eslint 的 AST 做开发、优化展示效果，且考虑到需求设计、周报、推广都由 Bug 一人完成，因此至少属于 2-1 的能力范围。
- Changes：从零到一建立了 lint 能力，项目中 lint 问题减少 2890->1168，高危问题（如之前出现过的、可能导致白屏的问题）被全面清除。
- Solution：业界调研、问卷、定制 lint 规则、分批推动。
- Evidence：FE 项目代码质量问题调研 & 治理方案、FE lint 规则评审
3. Branded App 业务支持与基建优化
- Scope：属于 enterprise 业务的中等复杂子产品（全部的底层模块 + 部分业务模块）。包含了 branded app 的鉴权复用、webview 通信、CI/CD 能力。
- Changes：在需求紧急、人力紧缺的情况下，通过技术的方式让 branded app 快速赶上 OBC 的进度，且让 RN 项目首次使用了 webview，作为技术积累。
- Solution：定制鉴权逻辑、引入 webview 通信、引入 B app 的 CD 流程。
- Evidence：/
4. @moego/pagespy 线上问题调试能力优化
- Scope：属于研发规范/体验业务的复杂模块。该能力在业界缺乏成熟方案，需要从底层协议和前端存储体系改造，且需要快速了解 Intercom 的集成。
- Changes：建立了用户全时段操作录制与回放体系，支持 CS 与研发快速定位并复现问题；通过自行编写压缩算法，将上传性能提升 60%、日志体积下降 40%；收到了来自 CS 团队的 主动正面反馈。
- Solution：在业界开源的 ospy 基础上进行二次开发，引入 IndexedDB 持久化日志；基于 gzip 协议自研分片压缩直传；与 Intercom 工作流打通，不打断 CS 与用户的工作流。
- Evidence：Pagespy 整个目录。
5. B Web 性能优化专项
- Scope：属于用户体验指标监控与优化模块，涉及多页面、多层级性能瓶颈排查与基础库 amos 优化。
- Changes：首页加载时间缩短至 270ms，lodging view 页面加载由 3s 降至 400ms，全局 LCP 提升 200–500ms，显著改善客户体验并助力关键签单。
- Solution：逐页分析性能瓶颈，优化表格渲染与日历计算逻辑；重构 amos 状态管理调度，引入 auto batching 与任务合并机制。
- Evidence：Amos Performance Opt - auto batch dispatch
6. Performance Budgets 建设
- Scope：同属于用户体验指标监控与优化模块；需跨越构建、依赖、Git 历史与性能采样，融合 AI 模型与算法，其复杂程度至少属于 2-1 难度范围。
- Changes：从零到一，实现提交级别的性能风险预估，解决“性能变差但无法追溯原因”的痛点，提升定位效率。
- Solution：基于 dpdm 的路由依赖分析、结合 git diff 与复杂度，构建影响因子；引入 AI 模型与遗传算法训练性能风险评分系统，并应用于 B Web。
- Evidence：Performance Budget 食用指南 - beta for B Web、食尾蛇工作流技术方案
7. BFF 基础建设
- Scope：当前属于业务团队，不由 platform 来主 own；但 Bug 参与了 BFF 从零到一的建设，并有着相当大的贡献。这些贡献需要有一定的跨端能力。
- Changes：接入了 AWS Secret Manager、制定了标准化路由与调试工具链，显著降低 BFF 开发/调试门槛，并让 BFF 研发可以使用后端的基建。
- Solution：实现基于文件即路由的系统，编写 create-route 脚本与 connect CLI 工具，统一开发体验；通过 AWS Secret Manager 推动 Node.js 项目部署与安全机制标准化。
- Evidence：/