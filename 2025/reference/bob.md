This content is only supported in a Feishu Docs
总览
在 EPR 领域划分中，有三大核心功能领域，分别是 Planning、Scheduling 和 Fulfillment，覆盖了从前期资源配置到中期客户预约，再到后期服务交付和信息管理的全过程。我作为 ERP 小组的核心后端工程师，主要负责规则定价、员工排班管理、住宿房间管理、智能调度几个关键模块。这些模块是连接前期 Planning 和核心 Scheduling 的桥梁，对整个预约系统的自动化和效率至关重要。
This content is only supported in a Feishu Docs
在 2025 年上半年初期（1-4 月），我主导了 Pricing Rule 和 Split Lodging 两大关键项目。我通过设计了一套可扩展的规则引擎，适配和优化了 Hybrid 预约下复杂计算逻辑，成功帮助 BD 商家实现了精细化的定价策略和高效的 Lodging 管理。
为应对业务发展的需要，5 月开始我加入 Grooming 业务组，承担了预约体系中最复杂的 Scheduling 域中 Smart Scheduling 模块的研发工作。我主导了 Book by Slot 项目的后端架构设计与实现。该模块的技术挑战在于多维度复杂查询逻辑。项目的成功上线，显著改善了 Grooming 商家的排班效率和收益。

一、Book by Slot 项目与 Smart Scheduling 适配
Situation
在 Grooming 团队成立初期，面临着磨合期短、交付周期紧迫的双重挑战。团队需要在较短的时间内，在没有历史设计文档、自动化测试等情况下，攻克业务逻辑复杂的 Book by Slot 功能。该功能是后续 Smart Scheduling 和 Online Booking 模块判断能否接受预约的关键依据，它的准确性和易用性直接影响商家的服务能力上限和资源利用率。
Task
我需要确保这项关键功能在预定排期内高质量上线。并且需要支持 T1、T2 级别核心商家的顺利 Onboarding，并达成提升用户坪效、显著降低用户流失风险的战略指标。
Action
面对挑战，我作为团队唯一的后端研发，扮演了技术负责人角色，采取了以下关键行动：
1. 主导技术方案的评估与选型，全面负责项目的研发进度管理与风险控制，确保产品稳步交付。
2. 梳理和完善 Time-based 与 Slot-based 功能的设计方案，并将核心逻辑与关键决策沉淀为技术文档，方便后续团队维护。
3. 帮助和指导外援研发快速进入项目开发，通过紧密合作，共同完成了核心功能的开发，确保项目上线进度无误。
4. 在没有文档、测试的情况下，梳理 Smart Scheduling 核心接口逻辑。该接口需要在多维度（Customer、Business、Staff、Service 等）多条件（Booking Request、Limitation）多场景（单 Staff 单 Slot、单 Staff 多 Slot、多 Staff 多 Slot）下，给用户提供一系列合适的 Time Slot。我通过多次重构和优化代码逻辑，添加详细的注释和单元测试，保证了功能的完整性。
5. 意识到数据埋点上的空白，独立开发并部署了白名单监控 Bot，实现了用户使用情况的自动化监控与实时推送，为团队提供了精准的数据洞察，间接推动后续的 Grooming Ops Review 工作。
6. 为了减轻整体迭代压力，我主动跨端推进进度，完成 Next Available Slot 前后端功能并完整无 Bug 上线。
Result
顺利在 530 前准时发布 Beta 版本，上线后服务运行稳定。白名单中高达 34% 的用户深度体验了产品，获得了积极的用户反馈。在团队内部我也获得产品、研发同事的正面评价。
Smart Scheduling 核心接口的 P95 从 2.24s 优化至 0.8s 左右，下降约 64%，极大地提升了系统性能，提高了用户体验。同时帮助商家极大提升调度效率，减少人工干预，优化 Staff 的资源分配。
[Image]
[Image]

二、Pricing Rule 重构与 Split Lodging 适配 
Situation
随着平台用户需求的增加，传统的 Discount Code（不适用涨价场景） 、Service Charge（不够灵活） 、Service Override（配置繁琐） 功能已无法满足复杂场景下的价格计算需求，例如多宠物、多晚住宿以及假期高峰定价。商家迫切需要一个能适应不同定价规则的解决方案，提高运营收入，确保价格精准并提升客户体验。
在后续的迭代中，发现旧数据模型可扩展性差，且定价规则与服务类型（Service Type）死板地强耦合。这导致商家在应用相似规则时，必须手动重复创建，操作流程极为低效，也为后续的新 UI 设计带来了巨大阻碍。
Task
在此背景下，我的核心目标是主导并交付一个全新的、高扩展性的动态定价系统。支持灵活、可复用的规则创建，保证计价逻辑的绝对精准。
Action
为达成上述目标，我执行了以下关键行动：
1. 设计和制定 Pricing Rule 数据模型，完成Pricing rules 后端设计方案 和 Holiday Pricing rules 后端设计方案的撰写与评审。在新设计中，将“定价规则”抽象为独立可复用的实体，从根本上解决了与服务类型的耦合问题，为系统的灵活性和未来扩展性奠定了坚实基础，Pricing Rule 2025 后端设计方案。
2. 独立承担了整个模块的后端开发工作，提交了所有核心代码。通过精心的代码重构，不仅实现了新功能，还优化了原有逻辑，显著提升了系统的可扩展性，支持灵活设置，帮助后续的 Peak date for first pet、Recurring surcharge、Vary pricing by zones 等需求快速迭代。
3. 为了确保交付质量，完成至少 60% 的自动化测试，提高功能可维护性，有效保障了复杂计价逻辑的稳定与准确。
4. 面对 Split Lodging 这一复杂场景，我主导设计了Split Lodging 后端设计方案，通过严谨的算法和逻辑处理，在搭配 Pricing Rule 时成功确保了跨越多条不同定价规则的订单也能精准无误地完成计价。
Result
共约 240 个 BD 商家使用（71% 的使用率），并创建了大约 940 条 pricing rule，使用记录达 82 万余次。通过定价规则功能的实施，帮助商家建立了一个无误差、具有竞争力的商业定价模型，显著提升了业务灵活性和客户满意度。同时也向行业标准对齐，提升 MoeGo 产品在 Boarding & Daycare 领域的核心竞争力。
[Image]
[Image]

三、智能化提效 Bot 迭代
Situation
在日常工作中，我观察到随着公司研发团队规模的快速扩张，团队及跨部门协作中存在一些效率瓶颈。例如，新员工入职信息、关键日期（如转正、生日）依赖人工追踪，容易遗漏且缺少人文关怀；开发流程中的 CI/CD 状态、Jira Ticket 更新等关键通知分散在不同平台，信息同步不及时；同时，在还没有接入飞书工具前，团队内部知识库的查询方式传统，无法快速、准确地获取信息。这些问题不仅影响了团队效率，也增加了不必要的人工沟通成本。
Task
我意识到这是一个可以通过技术自动化和智能化来解决的典型场景。出于对提升团队效率的热情和对前沿技术的浓厚兴趣，我在业余时间为自己设定了一个明确的任务：从零开始，独立设计、开发并部署一个集成化的 Slack 机器人，以解决上述痛点。
Action
我利用业余时间，系统性地推进了这个项目，全程负责了从设计到运维的每一个环节：
1. 利用 Bolt 与 Webhook，深度集成了 Slack 平台。同时，通过高可扩展的策略模式，给 DevOps Bot 设计了多达五个命令功能，轻松对接了 CI/CD、OpenAI、Jira 等，实现了事件驱动的实时功能。
2. 设计并实现了 HR Bot 可视化的管理界面，允许 HR 团队编辑源数据后全自动导入、更新全公司约 160 名员工的关键日期信息，还可以管理提醒消息，使用富文本定时定点发送公司级通知。对接大语言模型，设计向量化和多路召回流程，实现基础的知识问答和“猜你想问”功能。
3. 完整地实践了 CI/CD  流程，利用 GitHub Actions 实现了代码提交后自动化的单元测试、代码打包、Docker 镜像构建，并推送到私有 Docker Hub，并积极优化以减少五倍构建时间，加快问题解决速度和新功能上线时间。
4. 撰写HR Bot 使用指南，帮助同事快速上手使用。不断优化与增加新功能，把内部同时当做 Customer，在 #bot-feedback-fuel 反馈群中快速响应与解决使用问题。
Result
通过 Bot 的开发，不仅解决了团队的实际痛点，还通过年度报告和智能化工具增强了团队的互动与效率，为开发文化注入了更多创新元素。目前已经服务公司内部 70 名研发，累计推送近十万条通知，Jira 命令使用次数近五百次，AI 使用次数达百余次，受到公司内部人员一致好评，并已在司内全员推广（Slack Thread）。
同时还帮助公司实现了 HR 管理的数字化升级，同时增强了员工的参与感和满意度，为未来的智能化办公场景奠定了基础。上线以来，已经成功发送近五百条通知（示例消息：Slack Thread），至少减少 5000 分钟重复工作量。
[Image]
[Image]
[Image]

四、服务治理与优化
Situation
我所在的核心 Grooming 系统是公司的关键业务链路，随着业务量的快速增长，系统开始面临严峻的性能挑战。用户反馈部分核心接口响应迟缓，高峰期甚至出现超时错误。通过 Datadog 监控系统和日志分析，观察到部分服务响应时间超过 32 秒，P99 响应时间更是高达 5 秒以上，并且频繁触发 GC 告警，服务稳定性受到严重威胁。这不仅影响了用户体验，也对下游服务的 SLA 构成了风险。
Task
需要立即解决内存泄露问题，降低 Full GC 的频率，确保服务在高峰期能稳定运行。降低核心接口的 P99 响应时间，保障系统稳定性和提升用户体验。
Action
主动承担这些优化项目的负责人，执行了以下一系列具体的优化行动：
1. 针对 moego-svc-organization 服务中慢 SQL 治理，切换到从库数据查询，根除数据库性能瓶颈。
2. 内存泄露排查，通过梳理代码逻辑，修改异常代码，对 OBC SS 优化，减少错误，进而提升服务稳定性。
3. Smart Scheduling 逻辑底层深度优化，避免重复的网络和数据库请求，降低整体链路耗时，降低依赖服务的压力。
Result
慢 SQL 优化使得数据库主库峰值 AAS 从 3.4 成功降至 0.22，降幅达 94%，数据库负载恢复到极其健康的水平。 内存泄露问题被彻底解决，提高服务可用性到 99.99%，再未出现 OOM 异常，服务稳定性得到可靠保障。通过多次深入代码优化，降低 OBC 查询 TimeSlot 核心接口延迟，P95 从 815ms 降低至 609ms， 至少降低了 25%，提升用户体验。
[Image]
[Image]

五、团队协作与影响力
在 Book by Slot 项目中，有效地帮助项目新人快速加入，获得了积极的评价。
2025 上半年获得过 5-6 月 Leadership  Ownership 等奖项。
1. Leadership Ownership Award
2. Incident  Close-loop Award
3. AI Demo Award
4. Grooming EPD All-hands 主持
5. Tech Blog

总结
在 2025 上半年度的工作中，我在技术与业务两方面均有显著提升。技术上，已具备独立设计复杂功能、主导关键技术研发的能力，并能确保架构的合理性与长远的可扩展性。在 DevOps 效能提升方面亦有显著的成果。业务上，我展现了优秀的组织与协调能力，并能对负责的业务模块有深入的理解和把握。