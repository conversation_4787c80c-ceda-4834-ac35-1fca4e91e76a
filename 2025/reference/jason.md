Executive Summary
- 主导/深度参与 ERP 核心项目，覆盖从设计到落地，确保了业务的高质量快速扩展。
- 从不同视角出发，建立“三层预约模型”，为 Fulfillment 重构落地和长期演进奠定基础。
- 在 War Room 期间高质量交付多个定制化需求，核心参与并支撑了 Invoice V4 多轮迭代上线。
- 通过系统性的指导，成功带领新人快速上手并独立参与开发，有效提升了团队整体能力。

Project Review
Appointment 重构 (2 月 架构设计)
背景：旧有 Appointment 作为 ERP 核心模块，设计之初主要服务于 Grooming 单一场景。随着 BD 业务的快速发展，其历史包袱和扩展带来的复杂性逐渐凸显，难以高效支持跨天预约、差异化定价、不同业务视角下的查询，以及与 Waitlist/Booking Request 的强耦合限制了功能扩展，严重阻碍了新业务的扩展。
动作：
- 在重构之初，我与 Lead 频繁沟通，设计出了抽象的三层预约模型，通过对核心建模的重塑来解决历史遗留问题
  - Appointment（面向 Customer 预约视角）
  - Service/Addon Detail（面向 Pet&Service 视角）
  - Resource Daily Detail（面向 Resource 履约视角）
- 更早期便将 Booking Request 从 Appointment 中拆分出去，为后续 OB 业务的快速拓展提供了有力支撑
结果：
- 模型抽象化，提供高价值方案：产出 Appointment 重构方案，成为后续大规模落地重构的基础。它为团队提供了清晰的演进路径，使得团队在此基础上进一步简化模型，将 Service/AddOn Detail 合并成 Service Instance，并将 Daily Detail 最终抽象为 Fulfillment
- 极大地提升了预约系统对复杂场景的建模能力和业务适配性，大大提升了后续快速接入如 Group Class 等 care type 的能力

Invoice V4 (5 - 6 月 War Room)
背景：5 月 War Room 期间，多个 T1 商户（如 Central Bark、Red Dog）对现有订单体系反馈运维痛点，包括 Money flow、New invoice、Deposit by line items 配置受限等问题。旧模型已无法满足这些高度复杂且频繁变化的业务场景。更严重的是，系统存在 Appointment 的频繁修改导致 Order 数据不同步的问题，ERP 团队作为支撑，需要对订单涉及到预约的场景全面梳理，牵扯面广，同时还面临着必须在极短时间内交付一个全新的、稳定的 New Order 版本的巨大压力。
动作：
- 五一期间我紧急接手 Invoice V4 项目，面对多轮需求调整与高强度交付压力，与多方保持频繁沟通(Line item渲染、Late pick-up fee 适配)，快速梳理需求转化为可执行方案，保障了交付节奏
- 在面对大量历史债务问题时，积极对接上下游，确保数据一致性
- 同时协助团队成员梳理购物车页面调用链路复杂逻辑、提高整体协作效率和交付质量
结果：
- 项目历经数轮迭代后稳定上线，成功应对了高强度压力下的多变需求
- 为 Appointment 与 Order 解耦奠定了基础，使得整体业务职责划分更加清晰，数据也更加一致，大大减少了原本数据不一致带来的客诉

其他日常项目交付与细节优化（1 - 6月）
背景：除了核心的项目外，我也独立负责了多个中小型项目/需求的端到端交付。这些项目虽然规模不大，但对用户的体验和效率却至关重要。例如，在 Task Management 功能已上线的基础上，需要 Follow up 添加一些功能优化的同时，还需要解决一些影响用户体验的潜在问题；早期的预约系统仅支持单宠预约，随着业务发展，多宠场景的需求凸显，现有交互方式已无法满足；以及 War Room 期间，一些有 High risk churn 的用户提出的功能补充，比如 Calendar Card 需要拖拽复杂卡片，Evaluation 需要支持 Business Override 等等，这些需求都是需要短时间内快速交付的功能
动作：在这些项目里，我不仅是执行的角色，也更主动地参与设计和优化
- 在 Task Management follow up 里，完成产品需求的基础外，我主动识别到客户体验问题，多列排序问题与批量 assign staff 会重复通知的体验缺陷，提出合并+count优化并落地。
- 在 Multi-Pet 适配中，我在 BD 设计初期就进行了前瞻性的兼容性设计，使得在项目落地时，仅需改动上层入参校验并补充单测，便以极小的技术代价完成了高频使用场景的体验提升。
- 在 Calendar Card Reschedule 功能补充中，更早期重构时我就已考虑到扩展性与易用性，使得本次需求改动影响范围极小，通过 feature flag 逐渐放量，大大减少商家交互步骤，提高商家运营效率，并获得好评
[Image]
结果：
- 高效且高质量交付：两个项目均高质量稳定上线，无任何线上重大故障。特别是 Multi-Pet 项目，多宠场景的操作步骤从 N 步降到 1 步，显著提升了商家效率。
- 细节优化，提升用户体验：通过对 Task Management 的主动优化，避免了批量通知对用户的噪音

工程效率与质量提升
- Oncall：上半年在完成 3 轮值班，大多数工单当周闭环的同时，针对组内其他成员遇到的复杂的问题也能协助排查和给予建议；春节期间沉淀Associate service not found 排查手册，后续同类脏问题定位平均用时大幅下降，事后对 Date Type 写入添加校验，脏数据发生率↓。
- Incident/Postmortem：主导Postmortem - Exceed 24-hour Period Rule 未生效复盘，完成质量左移的同时并使 Platform 改进监控告警。
- 协同应急：Incident 101 中与小组成员联合排查并主动给出修复数据方案，推动相关小组成员完成事后改进获部门表彰
[Image]
- BatchQuickCheckIn 并行化：将串行改并行，P100 耗时从 10s 大幅下降至 1.5s↓。
Web / App Slowness Problem Fix List
[Image]
- Timeslot 查询 SQL 分流：Grooming-only 商家命中高效索引，P95 查询时延↓、CPU/IO 压力↓。
[Image]
- 数据与权限：对存量核心接口添加水平/垂直越权校验；越权风险↓。

团队影响与未来计划
团队影响力
- 新人 Onboarding：带教 1 人，成功完成业务/架构指导上手与 Oncall 实战教学，并在多次 PR review 阶段给予建设性意见。
- 知识沉淀：排查手册、重构方案设计、Postmortem 复盘，Incident 事后改进。
- 业务与技术赋能：在日常开发与 Oncall 过程中，持续为团队内外其他成员提供业务背景与技术实现的答疑支持，涵盖核心预约、Calendar、Service 等等
未来计划
- 重构：完成 Service 自定义 Care Type 模型的重构实现落地，并完成存量用户数据迁移。
- 平台化：完善自定义 care type 组合能力，沉淀接入文档，提升后期开发效率。
- 质量：做好质量左移工作，新增功能单测覆盖率达 80%，存量代码覆盖核心链路单测。
- 团队建设：深入协助其他成员 oncall 时，将排查经验沉淀文档化，以提升团队整体 oncall 效率。