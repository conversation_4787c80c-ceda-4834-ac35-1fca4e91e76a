1. 请填写你的姓名
   Dori

2. 你如何评价自己在 2025 年上半年的整体表现
   Exceed Expectations

3. 请在此填写你的 2025 年上半年工作总结
   请注意：​

   - 当自评为【E/O】，此项为【必填】；当自评为【M/N/P】，此项可【选填】；​
   - 绩效周期为 2025 年 1 月-6 月，请对周期内的工作进行回顾与总结；​
   - 可以从产出、对客户的价值、复杂程度等综合考虑，并提供相应事例；​
   - 填写时，可以选择在单独的文档中完成后再复制到系统中以防数据丢失。

## update CI for OBC

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/414
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/597
- 产出
  - 将 OBC 项目的 CI 由 jenkins 切换到了 github actions，对齐了团队内标准
- 对客户的价值
  - 对于前端来说，可以在 OBC 项目的发布上实践 trunk based release，让发布更加高效和高质量
  - 为后续的自动化部署奠定基础
- 亮点
  - 作为 OBC 项目的 owner 主动承担了切换和维护 ci 相关内容的职责
  - 在 Frank 提出迁移方案的第二天就积极参与并快速完成了 OBC 项目的接入
    ![](./update-ci-response.png)
  - 提出问题并推动约定了 trunk based release tag 的格式
    ![](./update-ci-release-tag-rule.png)

## introduce growthbook for OBC

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/442
- 产出
  - 将 growth book 引入 OBC 项目
- 对客户的价值
  - 建立了完整的 feature flag 管理流程，为后续众多 customized 需求 / AB Test 需求提供黑白名单支持基础
  - 研发可以在 OBC 项目上更好地进行渐进式发布，大幅降低全量发布的潜在风险

## order decoupling in online booking

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/440
- draft code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/420
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/428
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/439
- channel
  - `#order-decoupling`
- 产出
  - 产品上，解耦了 online booking 的提交和支付流程，将产品之前的先 pay 后 submit 改成了先 submit 后 pay，让流程更加合理和清晰
  - 代码上，重构了 OBC 项目 payment 相关的内容，将逻辑拆分成了可以动态组装的 `beforePayment` / `handlePayment` / `afterPayment`，通过抽象化支付逻辑，提升了代码的复用性和可维护性
  - 涉及到支付，但最终顺利安全上线且没有带来任何事故
- 对客户的价值
  - 防止出现 C 端用户在使用 online booking 时已完成支付但未创建预约导致的潜在资产损失问题
- 复杂程度
  - 在没有 payment squad FE 的合作下独自处理需求，和后端一起 own 需求
  - 初次接触不熟悉的 OBC payment 相关内容
  - 涉及支付时需要处理的细节很多，比如曾因为前后端需要借助 Stripe 组件天然支持的各种特性(3DS 验证、confirm payment intent 等)来处理 payment，导致设计方案和代码实践都需要推翻重来，[ref](https://moegoworkspace.slack.com/archives/C080Q6QFCNQ/p1735815302189039?thread_ts=1735556806.386949&cid=C080Q6QFCNQ)
  - 考虑到影响范围较大，需要引入白名单，而为了区分白名单/非白名单逻辑需要使用更好的设计，不能简单直接地去掉老的代码改成新的逻辑

## customized payment type depending on config

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/520
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/596
- channel
  - `#t1-ob-dynamic-deposit`
- 产出
  - 在确定的 ddl 下快速调整并完成了对 customized payment type 需求的支持
  - 在支持商家自定义需求的同时设计了较为灵活的配置，避免了硬编码，让后续想要有同样诉求的商家能够快速接入并使用上，降低后续类似需求的接入成本
- 对客户的价值
  - 让 B 端用户可以按照自己店铺的情况来给不同 service type 的 ob flow 设置不同的 payment type 或者不同的 payment options (eg. deposit / payment percentage)
- 复杂程度
  - 在设计稿和代码的基础上自主发现了许多 edge case (eg. 没有配置的 service type 下没有 fallback 的 payment type，[ref](https://moegoworkspace.slack.com/archives/C08N3PM59SQ/p1744621012858799))，推动了设计二次 review & confirm 用户需求，调整了产品和技术方案
  - 在代码和熟悉现有项目的基础上发现了逻辑上的 edge case (eg. 会和别的白名单冲突，需要确定优先级，[ref](https://moegoworkspace.slack.com/archives/C08N3PM59SQ/p1744614460060329))

## different services for different pets in online booking

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/476
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/496
- channel
  - `#feature-ob-multiple-pet-booking`
- 产出
  - 产品上，解决了之前 online booking 多个 pet 只能选相同 service 的问题
  - 代码上，下线了老的 bd 选 service 页面，让它和 grooming 选 service 页面统一，减少了维护成本
- 对客户的价值
  - C 端用户可以更加方便地为不同的 pet 选择不同的 service，降低了之前这种诉求被 online booking block 而给 B 端商家带来的额外工作量
  - 用户反馈
    ![](./different-services-feedback.png)
- 复杂程度
  - 代码修改量较大，新增 `3775` 行，移除 `1294` 行
  - 页面重构涉及到历史代码里比较 hacky 且难以理解的 DOM 动画 (即选择 service 后卡片滑动到顶部、取消选中 service 后卡片滑动到下方原来的位置)

## bundled addon for BD & extend dateType

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/620
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/4235
- channel
  - `#feature-boarding-auto-add-addons`
- 产出
  - OBC 新增对 bd service 的 bundled addon 支持，对齐了 grooming 的能力
  - 拓展了 dateType，新支持了 `First Day`、`Last Day` 等
- 对客户的价值
  - B 端用户可以用更准确的方式定义 service 和 addon 之间的绑定关系，B 端和 C 端使用时系统帮忙自动按照规则选中 service 捆绑的 bundled addon 可以帮助 B 端商家减少心智负担
  - B 端和 C 端用户可以使用更加灵活且符合现实诉求的 dateType (场景：假如用户诉求是在 boarding 第一天和最后一天加些服务，当 boarding appointment 需要调整 stay length 时候，用 Specific Day 就需要手动修改服务时间，用 First Day 或 Last Day 就不用二次调整服务的时间)
- 复杂程度
  - 不同 dateType 对应的 UI 和字段差异较大，梳理和开发起来较为复杂
    ![](./bundled-addon-complexity.png)

## waitlist for BD

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/497
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/523
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/3897
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/4497
- channel
  - `#feature-bd-waitlist`
- 产出
  - OBC 支持 C 端用户在进行 bd booking 且 lodging fully occupied 的时候能够提交 waitlist 请求
  - 主动 own 了本需求，积极和后端同学快速同步进度并暴露风险，推动需求平稳上线
    ![](./waitlist-owner.png)
  - 积极探索和发现众多的 edge case，让产品和技术方案不断完善，防止上线后出现事故和问题 (eg. 非常重要的 evaluation + waitlist 场景没有被考虑到，[ref](https://moegoworkspace.slack.com/archives/C08H4N55QGJ/p1751541016345499)；发现 waitlist 在不同入口下数据不一致，[ref](https://moegoworkspace.slack.com/archives/C08H4N55QGJ/p1751614003018559))
    ![](./waitlist-find-case.png)
  - 沉淀梳理了全部的 TODO，帮助产品团队清晰地了解后续开发的方向，[ref](https://moegoworkspace.slack.com/archives/C08H4N55QGJ/p1753092346145069)
    ![](./waitlist-todo.png)
- 对客户的价值
  - 用户反馈
    ![](./waitlist-feedback.png)
- 复杂程度
  - 代码修改量中等偏上，B 端和 C 端各自的改动量都在 `800` 行增减左右
  - 本来预期大部分内容都在处理 C 端，但在开发测试过程中才发现设计稿和产品上没有考虑的点很多，B 端新增了很多细节 case 和 工作量

## client portal pet list

- code
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/565
- channel
  - `#feature-obc-update-pet`
- 产出
  - C 端和 B 端支持对 pet vaccine 进行展示、更新和过期强提醒
- 对客户的价值
  - C 端用户可以在 pet vaccine 过期的时候被提醒到并且更新内容，B 端商家能够了解到 C 端用户宠物的身体近况 (主要是疫苗)，店内 bd / grooming staff 在操作的时候更放心
  - 用户反馈
    ![](./client-portal-pet-list-feedback.png)
- 复杂程度
  - 代码修改量较大，新增 `1144` 行，移除 `117` 行
  - vaccine 也是 OBC 项目里一个比较复杂的部分，包括新旧字段的兼容、general / specific vaccine 的要求、不同 required 状态的样式等细节处理
    ![](./pet-vaccine-complexity.png)

4. 2025 年上半年，你认为自己已经在哪些 Leadership Principles 表现出色
   ![](./leadership-aprinciples.png)

   - Earn Trust 赢得信任
   - Deliver Results 达成结果

5. 接下来，你希望自己可以提升哪些 Leadership Principles
   ![](./leadership-aprinciples.png)

   - Learn and Be Curious 求知若渴
   - Think Big 格局要大

6. 请在此填写 Leadership principles 表现出色的事例或希望提升的具体方向及资源支持
   请注意：​

   - 这并非是给组织或者 manager 看的作业，这是一个反馈和表达个人成长的渠道，此项可【选填】；​
   - 填写时，可以选择在单独的文档中完成后再复制到系统中以防数据丢失。

# 具体事例

## Customer Obsession (客户至上)

- 在 order decoupling 需求中，从用户真实痛点反推技术方案，用先 submit 后 pay 的方式保证了用户的资金安全
- 在 different services for different pets 需求中，用户给出的反馈直接证明方案解决了核心痛点

## Ownership (当家作主)

- 在 waitlist for BD 需求中，主动承担了 owner 角色，进行了合理的项目管理 (推进项目、暴露风险、及时同步)
- 在 waitlist for BD 需求中，拓展了前端的职责边界，把产品设计未考虑到的众多因素和后续 TODO 都澄清梳理了出来

## Invent and Simplify (敢想敢创，化繁为简)

- 在 order decoupling 需求中，将复杂支付流程拆分为可插拔的 before / handle / after 三阶段，方便进行黑白名单控制，将复杂的逻辑解耦成简单的模式

## Earn Trust (赢得信任)

- 在复杂的大需求中积极负责，保证了多个重要需求的平稳上线，代码质量和上线效果都较令人安心和信服

# 希望提升的具体方向

- Think Big (格局要大)：希望能从更高视角来设计理解需求，提升代码的健壮性

7. 25 年上半年，你是否承担 Manager 的角色
   否

8. 你是否希望在本次绩效周期提名晋升
   是的，我认为我已经稳定达到下一职级的能力要求
