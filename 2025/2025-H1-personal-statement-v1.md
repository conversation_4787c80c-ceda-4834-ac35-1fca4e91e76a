## 时间线
格式：PR 中第一个 commit 时间 - 发布时间

- 2024/12/25-2025/02/19 order decoupling
- 2025/02/06-2025/04/02 different services for different pets
- 2025/04/14-2025/04/17 customized payment type
- 2025/05/08-2015/05/20 client portal pet list
- 2025/06/03-2025/06/25 bundled addon for BD
- 2025/04/02-2025/07/21 waitlist for BD

部分需求包含假期/有优先级调整导致的暂时挂起。

## Highlight 项目
TODO

## 业务能力

### 深入理解负责模块，主动提出优化建议
核心模块：ob payment
- 如何复杂？
- 优化建议？
TODO

### 快速响应线上问题，积极主动跟进后续
核心项目：customized payment type
面对商家的紧急定制化需求，在确定的 DDL(当天) 下快速调整并完成了 customized payment type 的第一版 demo 支持。在开发过程中，主动发现了多个设计稿未考虑的 edge case，推动了产品和技术方案的二次 review，确保了需求的完整性。完整的方案确定后，1d 完成了开发并给出了产品体验地址，次日自测并完成了发布。
![](./customized-payment-type-timeline.png)

补充项目：oncall 问题
oncall 期间发现 CS 经常反馈 appointment 在 web 端能正常展示但 mobile 端无法展示的问题，在明确原因为数据错误后，积极尝试复现问题，并跟进错误问题修复，为 CS 同学提供了修复细节描述，帮助他们更好地与顾客进行交流。
![](./oncall-involved-and-followup.png)

### 有效推动多方协作，确保项目进展顺利
核心项目：waitlist for BD
在需求没有指定 owner 的情况下，不仅负责了前端开发，更主动承担了项目管理职责。通过积极与后端同学同步进度、暴露风险，确保了项目的平稳推进。在开发过程中，发现了产品设计未考虑到的众多 edge case，推动多方协作解决这些问题。发布完成后主动梳理了完整的后续 TODO，帮助产品团队明确了未来的迭代方向。
![](./cooperate-with-each-other.png)

补充项目：customized payment type
在时间紧急且功能核心(涉及到支付)的情况下，和后端积极分工并有效进行了自测，对业务结果承担责任。
![](./customized-payment-type-self-test.png)

## 技术能力

### 掌握复杂产品特性，承担核心模块开发
TODO
补充一些架构/时序图？

### 代码质量、拓展性、需求 bug 率
拓展性好：customized payment type
尽管 customized payment type 需求在最初宣讲时是仅供一个商家(red dog)使用的，但在方案设计时依然预见到了 general access 的可能，采用了较为灵活且可扩展配置方式，让后续有同样诉求的商家能够快速接入并使用上，降低了接入成本。

需求 bug 率低：order decoupling
虽然需求涉及到 ob flow 最后的核心支付流程，但有赖于良好的代码质量、全面的测试覆盖和黑白名单的处理，最终顺利安全上线且没有带来任何事故，这在涉及 payment 的需求中实属不易且极为重要。
![](./order-decoupling-bug-free.png)

### 积极和后端协商 API 设计
在多个需求中积极参与 API 设计讨论，为后端提供下游使用方的信息，并清晰明确地表达需求场景、字段诉求和考虑因素，确保了前后端协作的高效性。
![](./negotiate-api-fields.png)

### 前端工程化能力
积极维护 OB 项目 CI/CD，帮助推动了 trunk based development 的落地，提升了团队的发布效率和质量。
![](./ci-maintain.png)

App 原生打包 TODO

### 技术选型能力
map view TODO

## 团队贡献

### 开发流程改进建议
在 CI 迁移过程中，主动提出并推动了 release tag 命名规范的制定，建议也得到了认可并成功落地，提升了团队的发布流程标准化水平；同时提出的 pre-push check 也有效地帮助大家避免了不规范的分支命名。
![](./update-ci-release-tag-rule.png)

### 效率质量改进建议

### 进行业务成果分享

## 持续提升