# Job level description

## 关键词
独当一面的高级工程师
a.实习生/校招生导师
b.参与难题攻关
c.独当一面复杂功能和模块
d.技术选型能力/工程能力
e.初步跨端能力

PS：1-2 and 2-1 没有质变。只是需求和模块变复杂。

## 业务能力 (产品能力 + 业务推动协调能力)
1. 深入理解负责模块在产品中的定位、演进方向及背景，能够及时评估工作是否符合产品业务发展方向，并主动提出优化建议。对业务结果承担责任
2. 擅长运用数据分析方法，洞察负责模块中的问题点，并提出有效的改进方案
3. 快速响应并处理线上问题，积极主动跟进，协调推进内外部资源解决
4. 具备良好的组织协调能力，有效推动跨角色协作，确保项目顺利进展
5. 高效管理时间，根据任务优先级和重要程度，合理规划并妥善处理

## 技术能力
1. 对于复杂的产品特性，能够进行合理的技术设计，并实现功能。能够胜任各个模块的开发工作，并承担核心模块或者关键技术的开发。
2. 代码要求：在实现功能的基础上，要求能更好的兼顾到拓展性及性能等因素，同时bug率较低
3. 不仅了解自己负责模块的前端的实现，而且对于相关的服务端或者客户端的基本情况也有所了解，能够和后端或者客户端同事一起制定API接口
4. 能够有初步的前端工程化能力、技术选型能力

PS：复杂特性表现在业务复杂，可能多 bug、性能难、需要拓展的 abstraction 难设计和实现。

## 团队贡献 (分享、培养、创新、改进、影响力、管理)
1. 能够指导团队内实习生、应届生工作，帮助他们成长为合格的工程师，在工程师和产品中有很好的口碑
2. 对开发流程、效率、质量等方面积极提出改进建议，并能够推动落地
3. 有团队内的分享或培训，定期产出技术文章