# 晋升自述 - 高级前端工程师

## 概述

作为一名前端工程师，我在 2025 年上半年持续展现了独当一面的技术能力和业务推动力。通过承担多个复杂项目的核心开发工作，我不仅在技术深度上有了显著提升，更在业务理解、跨团队协作和工程化能力方面达到了高级工程师的要求。本文档将从业务能力、技术能力和团队贡献三个维度，结合具体项目实例，阐述我已具备晋升到下一职级的能力。

## 业务能力

### 1. 深入理解负责模块，主动提出优化建议

**核心项目：Online Booking Payment 模块重构**

作为 OBC 项目的 owner，我深入理解了 online booking 在整个产品生态中的关键地位。在 order decoupling 项目中，我从用户资金安全的角度出发，主动提出将原有的"先支付后提交"流程改为"先提交后支付"，有效防止了用户已支付但未创建预约的潜在资产损失问题。

**技术方案设计**：将复杂的支付流程抽象为可插拔的 `beforePayment` / `handlePayment` / `afterPayment` 三阶段模式，不仅解决了当前问题，更为后续的 customized payment type 需求奠定了架构基础。这种前瞻性的设计体现了我对业务发展方向的深度理解。

**业务价值**：该重构直接保障了用户资金安全，同时为后续多个商家的快速接入提供了技术支撑，体现了对业务结果的责任担当。

### 2. 数据驱动的问题洞察和改进方案

**核心项目：App 打包流程优化**

通过对 branded app 打包流程的深入分析，我发现原有流程存在效率瓶颈和知识传承问题。主动承担了 The Ruff Life 商家的 iOS 和 Android 端 app 发布工作，并在实践中沉淀了完整的[打包文档](https://moego.atlassian.net/wiki/spaces/ET/pages/525697084/SOP)。

**数据成果**：成功发布的 branded app 获得了 iOS 下载量 1.45k、Android 下载量 69 的良好表现，验证了 branded app 策略的可行性，为公司在该领域的发展提供了数据支撑。

### 3. 快速响应线上问题，积极协调资源

**核心项目：Dynamic Deposit 紧急需求**

面对商家的紧急定制化需求，我在确定的 DDL 下快速调整并完成了 customized payment type 的支持。在开发过程中，我主动发现了多个设计稿未考虑的 edge case，推动了产品和技术方案的二次 review，确保了需求的完整性和稳定性。

**协调能力**：通过与后端、产品、设计的密切协作，在保证质量的前提下快速交付，展现了良好的跨角色协调能力。

### 4. 跨角色协作推动项目成功

**核心项目：Waitlist for BD**

作为该需求的主动 owner，我不仅负责了前端开发，更承担了项目管理职责。通过积极与后端同学同步进度、暴露风险，确保了项目的平稳推进。在开发过程中，我发现了产品设计未考虑到的众多 edge case，并主动梳理了完整的后续 TODO 清单，帮助产品团队明确了未来的开发方向。

## 技术能力

### 1. 复杂产品特性的技术设计和实现

**核心项目：Different Services for Different Pets**

该项目涉及大规模的页面重构，代码修改量达到新增 3775 行、移除 1294 行。我成功统一了 BD 和 Grooming 的 service 选择页面，不仅解决了用户痛点，更减少了后续的维护成本。项目中需要处理历史代码中复杂的 DOM 动画逻辑，体现了我处理复杂技术债务的能力。

**用户反馈验证**：用户直接反馈该功能解决了核心痛点，证明了技术方案的有效性。

### 2. 代码质量：兼顾拓展性、性能和低 bug 率

**核心项目：Order Decoupling 架构设计**

在重构 payment 相关代码时，我设计了高度可扩展的架构，将支付逻辑拆分为可动态组装的模块。这种设计不仅满足了当前的白名单控制需求，更为后续的 customized payment type 等需求提供了良好的扩展基础。

**质量保证**：涉及支付的敏感操作，但最终顺利安全上线且没有带来任何事故，体现了代码质量的可靠性。

### 3. 全栈理解和 API 协商能力

**跨端协作经验**：在多个项目中，我不仅负责前端实现，还深度参与了 API 设计讨论。在 waitlist、payment 等复杂业务场景中，能够与后端同学一起制定合理的接口规范，确保前后端协作的高效性。

**技术广度**：具备了对相关服务端实现的基本了解，能够在技术方案讨论中提供有价值的建议。

### 4. 前端工程化能力和技术选型

**核心项目：CI/CD 优化**

作为 OBC 项目 owner，我主动承担了将项目 CI 从 jenkins 切换到 github actions 的工作，对齐了团队标准。在此过程中，我还推动约定了 trunk based release tag 的格式，提升了团队的工程化水平。

**技术选型**：在 growth book 引入、map view 重构等项目中，展现了良好的技术选型能力，为项目的长期发展奠定了基础。

## 团队贡献

### 1. 开发流程改进建议

**具体贡献**：在 CI 迁移过程中，我主动提出并推动了 release tag 命名规范的制定，提升了团队的发布流程标准化水平。这一改进建议得到了团队的认可并成功落地。

### 2. 技术分享和文档沉淀

**知识传承**：针对 solo plan、app 打包等复杂需求，我主动输出了详细的技术文档，并进行了团队分享。这些文档不仅帮助了当时的项目推进，更为后续类似需求的开发提供了参考。

**分享影响**：通过技术分享，帮助团队成员更好地理解复杂业务逻辑和技术方案，提升了整体的技术水平。

### 3. 复杂项目的技术攻关

**技术难点突破**：在 map view 重构、branded app 等项目中，我承担了核心技术模块的开发工作。这些项目不仅技术复杂度高，更对产品的市场竞争力有重要影响。

**影响力建设**：通过高质量的技术交付，在工程师和产品团队中建立了良好的技术口碑。

## 总结

通过以上项目实例的分析，我认为自己已经达到了高级前端工程师的能力要求：

**业务能力方面**：能够深入理解负责模块的业务价值，主动提出优化建议，快速响应线上问题，具备良好的跨角色协作能力。

**技术能力方面**：能够独立设计和实现复杂的产品特性，代码质量高且具备良好的扩展性，具备全栈视野和 API 协商能力，拥有扎实的前端工程化能力。

**团队贡献方面**：积极推动开发流程改进，主动进行技术分享和文档沉淀，在复杂项目中发挥关键作用。

我已经准备好承担更大的技术责任，期待在新的职级上为团队和公司创造更大的价值。
