# Self review

## 提示

Hi <PERSON>,

This is a friendly reminder that your Work Output Summary & Self-Review is still pending.

To Complete:
Include all significant work outputs and contributions throughout 2024, covering Jan to Dec
Focus on specific, measurable results
Draft your output summary in a separate document before copying to the system to prevent data loss
Feel free to fill in either English or Chinese
Due Date: January 6, 2025, 6:30 PM (CST)

Expected Time Needed: 30-40 minutes

For technical issues or process guidance, please contact <PERSON> or Melody for assistance:
Slack: @Iris / @Melody
Email: <EMAIL> / <EMAIL>

## 问卷

MoeGo Annual Performance Review-Output Summary and Self Review
You may finish the review in either English or Chinese.

### Please provide your name.

dori

### Please nominate your peer reviewers.(Max 6 nominations)

Note:

- Peer reviewers should be your customers. Customers could be internal or external to MoeGo, but they all consume and depend on your work output.
- You definitely have internal customers. They can be a collaborator within your own team, a stakeholder from an external team or another job function. Select your peer reviewers from this set of people.
- You may also have external customers. They can be the business owner who bought MoeGo or a staff member you support. Even though you can't select them as peer reviewers, you are welcome to quote them or use stats from them in your perf's content.

<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, pc, <PERSON>, <PERSON><PERSON>yal<PERSON>wang

### What valuable outputs did you create and produce for your customers (external and/or internal)? Start from the most valuable ones.

Note:

- What are the outputs?
- How are they valuable to your customers?
- What was the complexity you had to overcome to deliver them?
- What MoeGo value did you demonstrate along the way? (You can review MoeGo value at: https://shorturl.at/mVOED)
- If you feel you are going extra miles to achieve overall goals, please provide specific evidence.
- Please cover the entire year from January through December 2024.
- You may draft your output summary in a separate document before copying to the system to prevent data loss or you can simply paste a feishu doc link here (Remember open the access for all members in the organization!)

Things in () are for guidance. You don't have to use () in your content. Example: (the who) For this output, 30+ engineers are the customer. (the output) I introduced RsBuild and integrated it into our CI and devtools. (the value: before vs after) It reduced frontend build time from 15 to 5 minutes. (the value: customer's happiness) They are actively using it and rave about it over lunch. (the complexity, or what you had to do to transform your input to the output) The team had no idea. I researched RsBuild. I also had to attach a new cache system to achieve this improvement. (the MoeGo value) I ensured 100% test coverage and zero regression, demonstrating 'Set Your Standards High' through quality-focused delivery."

Example:
External customers and PM both are my customers. (the output) I built the epic (link to the epic). (the value: before vs after) T1 customer X will only sign the contract if they get this feature. (the value: customer's happiness) PM loved my initial implementation without any back and forth. And we got X usage from Y more customers. (the complexity + the MoeGo value) I had no PRD, no design, and there were tons of tech debts like X/Y/Z. I .....

#### 我自己的模版

- code：
- target group：
- output：
- value：
- complexity：
- MoeGo value：

#### Solo plan (new pricing plan)

- code：
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/1740
  - https://github.com/MoeGolibrary/moego-mobile/pull/1475
  - https://github.com/MoeGolibrary/MoeGo_Website/pull/160
- target group：
  - sales 对接的潜在用户
  - customer support 对接的 onboarding 用户
  - 大部分 T3 用户
- output：
  - 在 B web、B app、official website 多个项目内引入和支持了 solo plan
  - 在 MIS 上为 sales 提供了商家 customized contract 的功能
  - 进行了多次快速的 follow up
  - 需求虽然涉及 payment 以及至关重要的 upgrade flow，但严格把控了研发质量，没有产生重大 issue 或事故
  - 针对此需求输出过[文档](https://moego.atlassian.net/wiki/spaces/ET/pages/485819137/Solo+Plan)并进行过全员分享
- value：
  - 对 sales team，降低了 demo no show rate、提升了这其中 serious customer 的占比、让 sales 的工作和付出更加有价值
  - 对 onboarding team，将 onboard rate 提高到 90% 以上；对 product team，将 T3 以上的 3-month retention rate 提高到 90% 以上
  - MIS 上的 Platform sales 2024 共生成 691 条 sales link，签约 annual contract 200+；优化的代码让后续引入 boarding daycare pricing plan 更加无痛和快速
- complexity：
  - payment 相关逻辑复杂但代码却较为糟糕，没有很好的逻辑组织，本次需求中大范围重构了此类代码，让维护者和新人都能更清晰地 dive in，而不是断断续续地在代码里打补丁
- MoeGo value：
  - Focus on Customers：不仅为外部的 customer 即商家提供了更加合适的 pricing plan，也帮助了内部 customer 即 sales / cs team 让他们更好地触达目标顾客
  - Simple：在代码开发上注重可维护性和逻辑清晰

#### Map view reinvent (mobile tracking part)

- code：
  - https://github.com/MoeGolibrary/moego-mobile/pull/1628
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/2157
- target group：
  - T2 及以上的 mobile grooming 商家
- output：
  - 让商家能够在 mobile 端按需采集 assigned van 的 staff 的工作踪迹
  - 更新了旧版 map view 页面，商家可以在 B web 地图上看到实时的 van 踪迹变更
- value：
  - 提高了 multi van 商家的管理和信息获取能力，商家可以以可视化的方式更合理地 schedule appointment，让 MoeGo 成为 mobile grooming 产品中最好且唯一的解决方案，具有绝对的竞争力
  - 多商家主动向 customer support 表示新功能有 `so much more helpful`、是 `life saver`，提升了 MoeGo 产品的用户口碑
    ![](./review-map-view-1.png)
    ![](./review-map-view-2.png)
  - 移动端的 tracking 功能为 branded app 上向 c 端顾客展示 van 动态的功能提供了技术和数据的基础
- complexity：
  - 在不熟悉 mobile 项目的时候进行 react native background mode fetch location 选型
  - 在 Expo TaskManager 官方文档不够靠谱、最佳实践较为缺乏的时候学习探索出了合理可行的使用方式
- MoeGo value：
  - Be a Team Player：提供了走在行业前端的 map view 采集和呈现方式
  - Establish and Expand Ownership：在开发完毕后，考虑到不方便验证 mobile 端数据采集的频率，主动为 QA 同学提供了一个后台查看数据的页面
    ![](./review-map-view-3.png)
  - Be Relentlessly Resourceful：自主思考了很多边界条件和降级策略，确保了数据采集条件的准确性，上线后周知所有人注意事项
    ![](./review-map-view-4.png)

#### Online booking link path reform

- code：
  - https://github.com/MoeGolibrary/moego-online-booking-client-web/pull/242
  - https://github.com/MoeGolibrary/moego-client-libs/pull/25
- target group：
  - 所有使用了 online booking 的商家的顾客
- output：
  - 在向后兼容的前提下更新了 online booking page 路由组织规则，B 端用户仅需更新第三方内嵌 iframe 的 url 即可解决问题
- value：
  - 解决了由于 iOS 17 隐私策略更新导致的 Safari 在默认手机配置下无痕模式无法访问 online booking page 问题
- complexity：
  - iOS 17 较新，此问题没有细致的官方文档说明，业界没有太多针对此问题的解决方案
  - 需要向后兼容，收集来自 PM 和其他 FE 的建议，在此基础上探讨出了 quick fix 的可行性
  - 研究 wouter 库，改造现有的 router 模式
- MoeGo value：
  - Effective：有方案后快速实践，3/5 确定方案，3/8 给了体验地址，发现不 work 后也迅速 debug 找到解决方案
    ![](./review-online-booking-link-1.png)

#### Branded app build and archive

- target group：
  - 需要发布 branded app 的商家
- output：
  - 成功发布了 The Ruff Life 商家 [iOS 端](https://apps.apple.com/us/app/the-ruff-life-mobile-grooming/id6612021540) 及 [Android 端](https://play.google.com/store/apps/details?id=com.moego_brand.enc) app
  - 补充并沉淀了 app 打包相关 [文档](https://moego.atlassian.net/wiki/spaces/ET/pages/525697084/SOP)
- value：
  - 得到了 branded 商家的好评
    ![](./review-branded-app-1.png)
    ![](./review-branded-app-2.png)
  - 为 branded 商家提供了个性化的 c 端 app，目前仅 The Ruff Life 商家已达到了 iOS 下载量 1.45k、Android 下载量 69，通过数据也验证了 branded app 策略的可行性
    ![](./review-branded-app-3.png)
    ![](./review-branded-app-4.png)
- complexity：
  - app 打包流程复杂，工序和依赖事项繁琐，需要 step by step 的摸索和实践
  - 过程中经历了目前基建上热更新覆盖 branded info 的问题，为了 hotfix 需要频繁地打包，对初学者是一个不小的考验
  - 交接流程短暂 (差不多 2 week)，同时并行了其他业务需求
- MoeGo value：
  - Raise the Bar：在完全不了解 app 打包事项的时候，熟悉并完整地学习和交接了工作内容，拓展了自己在前端领域的边界
  - Establish and Expand Ownership：受团队人员变更影响，和 PM 沟通承担了交接期内的 app 打包责任，和团队成员共同协作，减轻了 PM 的担忧

#### Branded app two way message enhancement

- code：
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/2698
  - https://github.com/MoeGolibrary/moego-mobile/pull/1801
- target group：
  - 所有使用 message 功能的用户
  - 所有使用 branded app 的商家
- output：
  - 对 message 模块进行了 UI / UX 改造
- value：
  - 合作拟定了合适的技术方案，方便后续新增特殊链接和卡片
  - 新的 B 端 UI 让超链接 / 变量有了更好的收归和布局
  - 新的 C 端 UI 让 branded app 的 c 端用户收到特殊链接时有更易于操作的 UI，能看到链接的打开和填写状态
  - 新的代码易于拓展，让新增 variable 和 link 更简单
- complexity：
  - 影响面大，且 message center 是用户使用高频使用的核心功能之一
- MoeGo value：
  - Simple：选用了易于拓展的技术方案，避免了其他方案的大量刷数据步骤

#### agreement 和 appointment 绑定改造

- code：
  - https://github.com/MoeGolibrary/Boarding_Desktop/pull/1523
  - https://github.com/MoeGolibrary/moego-mobile/pull/1375
- target group：
  - 所有高频使用 agreement 的商家
- output：
  - 重构了部分古老的代码
  - 更新了 agreement 和 appointment 相关 flow
- value：
  - 解决了 agreement 和 appointment 在部分场景下缺失关联的问题
  - 优化了 agreement 提示混乱的问题，达到了在用户需要的场景下提醒的目的
  - 让代码更优雅可读
- complexity：
  - 代码较老，难以考证和迭代
- MoeGo value：
  - Focus on Customers：基于自己对项目的熟悉本领，及时发现和抛出用户 user story 环节可能遇到的遗漏点

#### Others

- branded app 支持 cancel 和 reschedule appointment
  - output & values：让 c 端用户可以自主取消/修改预约，并遵循 b 端商家的 cancellation / reschedule policy 和 cancellation fee
  - sparks：mobile -> c app 的换组初次业务接触，和大家合作愉快，需求顺利上线；完整负责和推进了 iOS & Android app 的上架过程
- home page 新增 edit lodging 入口
  - output & values：boarding & daycare 商家可以在 more icon 处直接编辑 lodging 而无需打开完整的 appointment 弹窗
  - sparks：c app -> merchant 的换组初次业务接触，和大家合作愉快，需求顺利上线
- 修复 google map marker flicker 问题
  - output & values：解决了 b app Android 端用户应用中
  - sparks：主动处理并修复反馈 issue 之外的场景，提供了不同 case 下不同的修复方案
- online booking additional contact 登录发送验证码
  - output & values：支持 online booking c 端用户用 additional contact 登录同时用 additional contact 接受验证码
  - sparks：快速响应不熟悉领域的用户需求，1d 内完成了宣讲和上线；在宣讲时候提出更优的解决方案 (eg. 这种 case 下可以直接发验证码给 additional contact 而不是再引入白名单走分支逻辑)
- online booking 支持 client update address
  - output & values：支持 online booking c 端用户更新 primary address，b 端用户能够有更加准确即时的 c 端用户地址信息
  - sparks：需求过程中考虑到了多种复杂情况
- send multiple images through MMS
  - output & values：b 端用户可以一次发送多张图片；c 端用户能够在 message 里直接看到图片而不图片链接；使用了图片压缩，拓宽了用户可上传照片的范围，在不太影响画质的情况下提升了用户体验；得到了用户的好评
  - sparks：发现问题并反馈给对应同学，使产品更加完善细致 (eg. 发现 upload 组件的某 icon 背景色不对，推进 infra 同学修改并应用到新需求中；发现多选图片没有给用户展示选中顺序，反馈给设计同学并实现了新方案)

### How would you rate your overall performance during 2024？

4

### How well have you been demonstrating MoeGo value？

- Note: You can review MoeGo value at: https://shorturl.at/mVOED

5

### Are you currently serving as a People Manager?

No

## MoeGo values
[Be a Team Player]

[Reality-based Feedback is a Gift]

[Focus on Customers]
- 提出更加一步到位的 customer friendly 建议

[Establish and Expand Ownership]
- 需求大体结束后依然会追踪同步 follow up 相关进度和发布内容给其他人
- 用心负责 review 每一个 assigned PR 并提出有效见解

[Be Relentlessly Resourceful]

[Stay Hungry, Stay Foolish] = [Raise the Bar] 

[SEE]
Simple
- 及时解决合理的 PR comment 以减少代码中的 bad taste
Explicit
- 每次线下 sync 会将对应的结论同步给所有人
- 有讨论时会将上下文阐述清楚避免误解
Effective
- 高效完成紧急任务


# Manager review

## 问卷

### 问题 1

According to your people manager's work outputs, how would you rate your people manager's performance?
Please provide specific examples and facts.

Note:​

- Be precise, objective, and fact-driven.​
- Your review should be based solely on your direct observations and interactions with your people manager.​
- Draft your feedback in a separate document before copying to the system to prevent data loss.​

Example:​

In Q4, he/she made several critical decisions that directly impacted our payment feature adoption. He/she made the decision that our team should build the auto-refund epic (JIRA-1234) when facing pressure from both our T1 customer Shopify and our Product team. This feature was a deal-breaker for Shopify's $xx contract renewal. His/her decision to prioritize a flexible refund policy configuration proved successful - the PM approved our initial implementation without revision requests, and within the first month, we saw xx+ refund transactions from xx new merchants. What makes this achievement more impressive is that he/she guided the team to break down these technical debts systematically and established clear guidelines for the team to follow.

注：​

- 准确、客观、以事实为导向
- 你的评估应该完全基于你的直接观察和与员工经理的互动。​
- 在复制到系统之前，在单独的文档中起草您的反馈，以防止数据丢失

例子：​
在第四季度，他/她做出了几个直接影响我们支付功能采用的关键决定。当面临来自 T1 客户 Shopify 和产品团队的压力时，他/她决定我们的团队应该构建自动退款史诗（JIRA-1234）。此功能是 Shopify 续约 xx 美元合同的交易破坏者。他/她决定优先考虑灵活的退款政策配置，这一决定被证明是成功的——项目经理批准了我们的初步实施，没有提出修改请求，在第一个月内，我们看到了 xx 家新商户的 xx+笔退款交易。更令人印象深刻的是，他/她指导团队系统地分解这些技术债务，并为团队制定了明确的指导方针。

### 问题 2

According to your people manager's work outputs, how well does your people manager demonstrate MoeGo value?
Please provide specific examples and facts.

Note: ​

- Be precise, objective, and fact-driven.​
- Your review should be based solely on your direct observations and interactions with your people manager.​
- Draft your feedback in a separate document before copying to the system to prevent data loss.​

Example:​
When Shopify requested this feature as a requirement for their $xx contract renewal, he/she took full ownership of the problem and drove changes proactively. Instead of waiting for complete specifications, he/she made the decision to start with a simplified version that addressed core merchant pain points. His/her approach was both simple and effective - he/she broke down complex refund scenarios into three clear states and designed a flexible configuration system that PM approved without revision. Throughout the project, he/she set and maintained high standards for code quality and system reliability, which has become the benchmark of our team.

注：​

- 准确、客观、以事实为导向
- 你的评估应该完全基于你的直接观察和与员工经理的互动。​
- 在复制到系统之前，在单独的文档中起草您的反馈，以防止数据丢失

例子：​
当 Shopify 要求将此功能作为续订 xx 美元合同的要求时，他/她完全掌握了问题的所有权，并主动推动了更改。他/她没有等待完整的规格，而是决定从解决核心商家痛点的简化版本开始。他/她的方法既简单又有效——他/她将复杂的退款场景分解为三个明确的状态，并设计了一个灵活的配置系统，PM 无需修改即可批准。在整个项目过程中，他/她为代码质量和系统可靠性设定并保持了高标准，这已成为我们团队的基准。

### 问题 3

How would you rate his/her leadership as a people manager?
Note: Leadership encompasses the skills to make smart decisions, develop teams, and lead team deliver results.

Please provide specific examples and facts.

Note: ​

- Be precise, objective, and fact-driven.​
- Your review should be based solely on your direct observations and interactions with your people manager.​
- Draft your feedback in a separate document before copying to the system to prevent data loss.​

Example:​
Based on my experience working directly with my leader, I've observed strong leadership capabilities. For instance, when our team was tasked with launching a new customer feedback platform with only two weeks' notice, my leader made the strategic decision to reorganize our workflow and prioritize key deliverables. My leader invested time in upskilling the team through targeted training sessions on the new feedback system and provided clear direction while remaining open to team input.​
​
Throughout the project, my leader maintained close progress tracking through daily check-ins, helping us promptly identify and resolve technical bottlenecks. While setting high quality standards, my leader also provided practical solutions and resources when we encountered challenges with the data integration. This hands-on support and clear quality expectations drove us to deliver the platform not just on time, but with excellent user experience and robust functionality.

注：​

- 准确、客观、以事实为导向
- 你的评估应该完全基于你的直接观察和与员工经理的互动。​
- 在复制到系统之前，在单独的文档中起草您的反馈，以防止数据丢失

例子：​
根据我与领导直接合作的经验，我观察到了很强的领导能力。例如，当我们的团队被要求在两周内启动一个新的客户反馈平台时，我的领导做出了重组我们的工作流程并优先考虑关键交付成果的战略决定。我的领导投入时间通过针对新反馈系统的有针对性的培训课程来提高团队的技能，并提供了明确的方向，同时对团队的意见持开放态度。​
​
在整个项目过程中，我的领导通过每日签到保持密切的进度跟踪，帮助我们及时识别和解决技术瓶颈。在制定高质量标准的同时，当我们遇到数据集成的挑战时，我的领导还提供了实用的解决方案和资源。这种实践支持和明确的质量期望促使我们不仅按时交付平台，而且提供出色的用户体验和强大的功能。

## PiEgg

### 问题 1

工作产出上，推动了很多大项目，为 MoeGo 在 Enterprise、Boarding 和 Daycare 市场占据份额提供了有力的保障；项目管理上，事无巨细地跟进团队成员的需求安排，和产品设计后端协商需求方向；技术能力上，严格 review 项目代码，对成员能够提出有见地的技术指导，同时会主动调研推进技术需求，比如为提高前端研发开发体验迭代 MoeGo chrome extension。

### 问题 2

- Be a Team Player：协调多人前端团队和后端、产品、设计共同推进了大型项目的落地，让 MoeGo 在市场上有了超高的竞争力
- Focus on Customers：不仅会在需求中关注终端用户的体验，提出相关的优化和改进见解，还会关心研发的开发体验，推动组员解决了项目构建慢的问题
- Establish and Expand Ownership：不仅对 BD squad 非常负责，下半年过渡时期对 C App Squad 也很负责，具有很令人值得学习的 ownership
- Be Relentlessly Resourceful：有很高的前端领域的专业技能水平，在遇到技术问题时总能从 PiEgg 那里得到视野和指引
- Raise the Bar：快速面对两个超大型项目也能推进得很成功，没有因为时间的紧迫而产生难以维护的代码，也没有给后面的人埋坑
- SEE
  - Simple：推进团队产出的代码朝着熵减的方向迭代，让成员能够在代码和需求讨论上都以简洁的方式进行
  - Explicit：在需求会上可以很清晰快速地发现重点和不需要纠结的部分，在遇到 oncall 问题时也能快速准确地找到模块负责人并提供建议
  - Effective：不开无用的会，高效把控会议节奏和内容，让每一分钟都不浪费和消耗

### 问题 3

- 响应快速且到位：在 #feature-boarding-daycare channel 里，经常有来自美区和国区的产品和设计的艾特，PiEgg 总能及时给出答案和 CTA；在下班时间遇到需要 hotfix 的线上反馈，而自己在路上无法修改代码提交构建时，PiEgg 会快速帮忙处理，减少线上 bug 的影响面
- 管理范围大、管理效果好：推进两个超大项目，管理十人前端团队并事无巨细地关注每个人手头的需求，促进了需求平稳顺利地开发和发布，对 MoeGo 在 Boarding 和 Daycare 的成功推进上起到了至关重要的作用
- 及时应对组织变动、承担更大的管理责任：在公司频繁 reorg 和频繁人员变动导致组织混乱的背景下，PiEgg 能够帮助青黄不接的 C App 团队，承担起 FE leader 的职责，组织每周的周会来同步需求进度、参与需求 KO 给出合理的建议和判断
- 让团队信息透明：所有工作和讨论消息都会在对应的 channel 中以 thread 的方式收归，避免了 DM 造成的信息不透明和 DM 消息多次转发造成的信息失真
- 让团队代码规范：PiEgg 会认真 review 团队成员的 PR 并给出宏观视角上的有效建议，组织大家讨论制定了团队内的代码规范，避免 bad-taste code 的出现

# Peers review

## 问卷

### 问题 1

According to the work outputs this colleague delivered to or collaborated with you, how would you rate this colleague's performance?

Note: Your review should be based solely on the work outputs this colleague delivered to or collaborated with you.

Please provide specific examples and facts.

- Note: ​

- Be precise, objective, and fact-driven. ​
- Be specific about which work output or interaction you are referring to. ​
- You may draft your feedback in a separate document before copying to the system to prevent data loss.​

Things in () are for guidance. You don't have to use () in your content.​

Example:​
As an engineer from the frontend team, (the output A) I directly worked with he/she in the CI/CD automation project. In this project, he/she developed and implemented an automated testing framework. (the value: before vs after) Our team's deployment time decreased from 2 hours to 30 minutes, and test coverage increased from 65% to 95%. (the value: customer's happiness) All engineers are actively using it and rave about it over lunch.

根据该同事交付给您或与您合作的工作成果，您如何评价该同事的表现？

注意：您的审核应仅基于该同事交付给您或与您合作的工作成果。

注意：​

- 准确、客观、以事实为依据。 ​
- 具体说明您所指的工作输出或交互。 ​
- 您可以在复制到系统之前将反馈起草在单独的文档中，以防止数据丢失。​

() 中的内容仅供参考。您不必在内容中使用 ()。​

示例：​
作为前端团队的工程师，（输出 A）我直接在 CI/CD 自动化项目中与他/她合作。在这个项目中，他/她开发并实现了一个自动化测试框架。 （数值：之前 vs 之后）我们团队的部署时间从 2 小时减少到 30 分钟，测试覆盖率从 65% 增加到 95%。 （价值：客户的幸福）所有工程师都在积极使用它，并在午餐时对它赞不绝口。

### 问题 2

According to the work outputs this colleague delivered to or collaborated with you, how well does this colleague demonstrate MoeGo value?

Note:

- You can review MoeGo value at: https://shorturl.at/mVOED.
- Your review should be based solely on the work outputs this colleague delivered to or collaborated with you.

Please provide specific examples and facts.

Note: ​

- Be precise, objective, and fact-driven. ​
- Be specific about which work output or interaction you are referring to. ​
- You may draft your feedback in a separate document before copying to the system to prevent data loss.​

Things in () are for guidance. You don't have to use () in your content.​

Example:​
(the output A) In the CI/CD automation project, (the MoeGo value) he/she demonstrated "Focus on Customers" by conducting systematic interviews with all 15 team members to identify pain points, then designed and delivered 3 automation solutions. (the Moego value) he/she ensured 100% test coverage and zero regression, demonstrating 'Set Your Standards High' through quality-focused delivery.

根据这位同事交付给您或与您合作的工作成果，这位同事展示 MoeGo 价值的程度如何？

注：

- 您可以在以下网址查看 MoeGo 的价值：https://shorturl.at/mVOED。
- 您的审核应仅基于该同事交付给您或与您合作的工作成果。

请提供具体例子和事实。

注：​

- 准确、客观、以事实为依据。 ​
- 具体说明您所指的工作输出或交互。 ​
- 您可以在复制到系统之前将反馈起草在单独的文档中，以防止数据丢失。​

() 中的内容仅供参考。您不必在内容中使用 ()。​

示例：​
（输出 A）在 CI/CD 自动化项目中，（MoeGo 价值观）他/她通过对所有 15 名团队成员进行系统访谈来识别痛点，展示了“以客户为中心”，然后设计并交付了 3 个自动化解决方案。 （Moego 值）他/她确保了 100% 的测试覆盖率和零回归，通过以质量为中心的交付展示了“设定高标准”。

#### 我自己的模版

- Be a Team Player：
- Focus on Customers：
- Establish and Expand Ownership：
- Be Relentlessly Resourceful：
- Raise the Bar：
- SEE
  - Simple：
  - Explicit：
  - Effective：

### 问题 3

Please provide actionable suggestions for improvement.

## Freeman

### 问题 1

在过去的一年里和 Freeman 有过多次合作，包括 online booking 项目和后来的 boarding daycare 项目。他有很强的责任心且能做到高效的交付，是我所有合作的后端中最令人感到放心的一位。

具体来说，在 online booking 项目中，Freeman 负责了多个需求的设计与开发 (online booking update address / google reserve / etc.)，这些需求涉及了多个服务调用和复杂的业务逻辑，他不仅在前期开发能够想到很多问题，在后期维护方面也非常给力，oncall 被艾特到也是独当一面、积极处理。Freeman 也不会让接口停留在只是能用的水平，而是倾向于以合理的方式向 best practice 的方向靠近和实现，他还很注重接口性能，这让终端用户能够更丝滑地使用系统。除此之外，在合作体验上，他在需求交付上非常及时且稳定，几乎没有接口频繁变动的问题，这对前端开发的推进帮助非常大。

此外，Freeman 在面对复杂或挑战性较大的问题时，表现得非常独立且具有解决问题的能力。举个例子，在我们处理 Google Reserve 相关问题时，Freeman 不仅迅速响应并修复了涉及多个系统的 bug，还成功推动了与 Google 团队的合作 (付出的努力包括各种和 Google 官方的邮件来回)，最终帮助超过 900 个商家实现了 reserve with Google。其他在他职责范围内的需求他也会积极跟进，在此就不赘述了。

### 问题 2

在多个项目中，Freeman 始终展现了 MoeGo 的核心价值，特别是在 "Be a Team Player" 和 "Raise the Bar" 方面。

Be a Team Player：他总是能和团队其他成员保持紧密合作，特别是在需求沟通与接口设计时。需求的开发期，Freeman 都能够迅速回应前端的需求，提出清晰的接口规范，并且确保接口交付稳定，这种高效的沟通和交付让前后端协作变得非常顺畅。

Focus on Customers：在工作中，Freeman 非常注重终端用户体验，甚至会在需求中主动给设计同学提出一些用户视角上的设计建议 (之前和 Shang 合作的时候)。Freeman 总能准确把握需求中的潜在问题，并积极推动解决，这保证了我们交付的每一项功能都能最大程度地满足终端用户的需求。对于内部 customer 即 EPD 本身，他会积极推动技术上的迭代，提升内部研发开发体验，减轻开发人员心智负担，这也是 focus on customers 的一种体现。

Establish and Expand Ownership：对于每一个需求，Freeman 都展现出非常强的责任感。无论是在 online booking deposit 还是其他小需求中，他都能完全掌控项目的每一个环节，并对最终的交付负责。这种全程的责任心使得项目能够顺利推进，并且质量有保障。

Raise the Bar：他不仅仅满足于完成任务，而是会从更高的角度去考虑问题，推动技术上的提升。在 Google Reserve 项目中，解决了与 Google 团队的多次技术对接问题，最终让超过 500 个新商家成功接入，帮助商家显著提高了 online booking 的转化率。这种超出自身职责范围的沟通，充分展示了他在不断尝试 raise the bar。

SEE (Simple, Explicit, Effective)：Freeman 在接口设计和需求实现时，总能做到简单、明确且高效。即使是很小的需求诸如 agreement 优化，他也会认真设计，争取在有限的时间里不仅能够实现业务需求，同时能够保障性能、保障后续迭代的可维护性。这种高效且简洁的技术解决方案、高效准确的沟通和实践，充分地体现了 SEE 原则。

总的来说，Freeman 不仅技术能力突出，而且非常注重团队合作和客户需求。他对每个项目的高责任心、对细节的关注，以及对技术创新的追求，都让他成为了团队中不可或缺的一员。

### 问题 3

无

## PC

### 问题 1

过去的一年，我和 pc 在几个项目上有过合作，主要是 Solo Plan、Desktop Map View Reinvent 和 Support MMS Send 这几个功能。在这些项目中和他的合作很愉快，最终的提测质量和上线效果也很不错。

在 Solo Plan 项目中，pc 一人承担了所有后端职责，他在不熟悉 payment 的情况下积极主动地寻求 ritchie 的协作，靠谱地给出了新的解决方案，最终顺利上线。我们共同合作的这一改动直接影响了 Sales team 的转化率和 Onboarding team 的工作效率，新的 pricing plan 对 MoeGo 先进的定价模式和流畅的内部协作起到了至关重要的作用。

在 Desktop Map View Reinvent 项目中，我和 pc 的合作主要在 mobile van tracking 部分 (另一部分的 web 端是 kangkang 处理的)。过程中 pc 积极主动考虑 tracking 接口的各种要求，包括调用频率、数据存储时限、多设备数据如何区分等等，他对 MoeGo 产品充分的了解帮助了我们推进项目的平稳落地。这一功能不仅帮助商家更好地管理其业务，还为 C app 后续的 tracking 功能奠定了基础，对 MoeGo 后续面向 mobile grooming 的发展有很重要的影响。

另外，在 Support MMS Send 项目中，pc 将 twilio MMS 功能接入 MoeGo，允许商家通过 MoeGo 向客户发送多媒体信息。他完整地完成了这项需求，并且在提测后发现了事实上 twilio MMS 发送不支持 webp 这一问题，很细心负责。

### 问题 2

在我和 pc 的合作中，他始终展现了 MoeGo 的价值，特别是在 "Be Relentlessly Resourceful" 和 "SEE" 方面。

Be Relentlessly Resourceful：在每个项目中，pc 都能及时靠谱地响应前端的诉求。在面对一些复杂需求时，他总是能够快速找到切实可行的解决方案。例如，在 Solo Plan 的过程中，pc 能够肩负起不熟悉的 payment 模块，积极组织和参与多次的技术讨论，并且在 Desktop Map View 和 MMS Send 项目中，他也都能在有限的资源和时间下，找到最合适的解决方法，保证项目顺利落地。

SEE (Simple, Explicit, Effective)：pc 在项目中始终保持了极简的设计思维，确保每个解决方案都简单、易于理解且不会扩大影响面。在 Desktop Map View Reinvent 中，他给出的接口很清晰。在 MMS Send 功能的实现中，他的设计方案确保了产品功能扩展的同时，不增加系统的复杂度，保持了 MoeGo 的稳定性和高效性。

### 问题 3

在一些功能迭代上可以不那么守旧或者担心它对现有模块的冲击，大胆一些。

## Kangkang

### 问题 1

在与 kangkang 合作的 Map View Reinvent 项目中，他表现得非常出色。尽管这是他入职的第一个需求，但他展现出了极强的自主能力和解决问题的能力。因为这个需求我和他负责的是不一样的模块，交集很少，导致我能给他提供的业务代码上的帮助也很有限，但他依然能 handle 住。包括但不限于 Google Map 各种 API 的使用、Full Calendar 的熟悉和接入，对于一个刚入职的新人来说，是不小的挑战，但他完成得很好。在项目中，kangkang 成功地重构了旧的地图功能，并引入了新的 Schedule/Reschedule、Van Tracking、Timeline 和 Driving Time 功能，为 T2 及以上的 Multi Van 商家提供了非常高效的管理和调度工具。

他在项目中的工作非常独当一面，能够在没有过多指导的情况下摸索出合适的实现方式，比如在地图渲染库的选择上，独立实现了一套新的 Google Map 渲染方案，成功降低了地图控件的复杂度。此外，kangkang 的代码组织很清晰、模块化，这使得后续的开发和维护变得更加高效。每次 PR 提交后，他总是仔细阅读并认真处理所有的反馈，展现出对代码质量的高度重视。

### 问题 2

Be Relentlessly Resourceful：在 Map View Reinvent 项目中，kangkang 充分展示了他的可信任。在遇到复杂的 Google Map & Full Calendar 时，他能够高效地找到解决方案，并将其成功落地。例如，针对旧地图功能的重构，他不仅仅依赖于现有的技术栈，而是独立完成了 Google Map 渲染库的选择和实现，极大简化了代码复杂度并提高了性能。

SEE (Simple, Explicit, Effective)：kangkang 的工作方法始终遵循简洁、明确和有效的原则。在 Map View Reinvent 中，他通过对代码结构的优化，简化了开发流程，提升了功能的可维护性，为后续的快速迭代提供了技术上的基础。对于 oncall 问题的及时响应和快速解决也很好地诠释了 Effective 这一 MoeGo value。

Focus on Customers：在需求中，kangkang 注重从客户的角度出发，优化产品体验，给设计和产品提出了挺多有效的建议。在 Map View Reinvent 中，他的工作让 T2 及以上的 Multi Van 商家能够更轻松地管理员工和预约之间的关系，得到了用户的积极反馈。在商家体验方面，他的贡献直接提高了产品的市场竞争力。

Be a Team Player：尽管 kangkang 在多个项目中展现了极强的独立工作能力，但他依然能与团队保持紧密协作，确保工作目标一致。在 Map View Reinvent 项目中，他和我、设计、后端都配合得非常好，沟通也非常及时，确保了项目按时高质量交付。

总的来说，kangkang 在多个项目中展现了出色的技术能力和解决问题的能力，始终能够高效地推动项目进展，并在过程中展现了良好的团队合作精神和用户导向的工作态度。

### 问题 3

需求中遇到问题可以更大胆地抛出问题。

## Bug

### 问题 1

在多个项目中与 Bug 的合作中，他展现了极强的技术能力和团队合作精神，且能够高效解决复杂的技术问题，推动项目进展。在 Branded App two way message 的开发中，Bug 成功解决了图片上传和后端 API 压力问题。他主动承担了优化图片上传体验的任务，显著提升了图片上传性能，并减少了系统负担。此外，Bug 解决了图片质量过高对后端造成的压力，确保系统稳定。

在多商家定制化能力建设中，他主动设计并实现了一套自定义配置的平台，让产品和设计能够更快地 onboarding 新商家，确保系统能够支持更多商家灵活接入。

在 dpdm-fast 工具重构中，Bug 能够快速学习 Rust，并成功将其应用到 CI 流程中，使得 B Web 和 B App 项目的构建时间大幅缩短。这一优化直接提升了团队的效率，并为后续的开发工作节省了大量时间。

Bug 会主动发现和解决目前研发流程、产品设计中的问题，在解决问题和推动进展方面的表现非常突出，他的贡献对团队和项目的成功具有重大影响。

### 问题 2

1. Be a Team Player，Bug 在多个项目中表现出色的团队合作精神。例如，在 Branded App 多商家定制化能力建设过程中，尽管团队面临着人力和技术上的挑战，Bug 能够与不同团队成员紧密合作，积极推进需求并解决技术难题。他不仅积极承担责任，还主动帮助解决团队成员的技术问题，体现了团队协作精神。

2. Focus on Customers，在 Branded App 双向消息系统的开发中，Bug 始终围绕商家的需求展开，帮助商家大幅降低了 SMS 费用，提升了沟通效率。他关注商家的痛点，优化了图片上传体验，并通过技术创新为商家提供更好的工具，最终推动了商家的持续好评，体现了“以客户为中心”的价值观。

3. Establish and Expand Ownership，在 Branded App 多商家定制化能力建设中，Bug 主动承担了热更新能力建设任务，并迅速完成了调研和实现工作，确保了商家的按时上线。在 dpdm-fast 工具重构中，虽然该任务并未列入规划，他依然主动识别出性能瓶颈，承担起工具重构任务，并成功实现了性能提升。这些行为展现了 Bug 在工作中的主人翁精神和对项目的高度责任感。

4. Be Relentlessly Resourceful，Bug 在面对技术难题时表现出了极强的应变能力。例如，在重构 dpdm-fast 工具时，尽管他对 Rust 的掌握仅限于基础水平，但通过自学和积极借助 AI 工具，他最终完成了这一任务，展现了他强大的自我驱动能力。

5. Raise the Bar，在多个项目中，Bug 都致力于提升系统性能和用户体验。例如，在图片压缩能力提升的项目中，他主动设计并实现了一套高性能、高压缩率的图片压缩 SDK，成功降低了存储成本，并为团队积累了宝贵的技术资产。Bug 始终追求技术上的卓越，通过不断优化和创新，推动团队的技术水平向更高标准迈进。

6. SEE (Simple, Explicit, Effective)，Bug 在 channel 里会很及时地响应来自产品设计的提问，开发也非常高效。

### 问题 3

在开发过程中可以更准确地识别出重要程度，避免投入过多资源在低优先级的事情上；可以多将方案同步给团队成员，进行充分的讨论后再开发，避免最后已经实践完了大家才知道方向。

## Nain

### 问题 1

在与 Nain 的合作过程中，他积极主动参与 branded app 事项的讨论和推动，为 c app 团队的项目推进提供了强有力的支撑。
Nain 在 Branded App 的需求迭代中发挥了关键作用，成功推动了产品从 0 到 1 的落地，并为多个商家提供了定制化的品牌应用方案，不仅获得了商户的积极反馈，还为公司带来了新的增长点。

In-app Message 功能的落地
Nain 为 Branded App 成功实现了 In-app Message 功能，帮助商户节省了大量的短信费用，同时提升了用户体验。这项功能不仅增加了 Branded App 的卖点，还为 PM 提供了更具吸引力的营销工具。通过将 IM 能力做成 SDK，Nain 为团队建立了可复用的基建能力，增强了后续产品开发的灵活性。

系统日志建设
在系统日志方面，Nain 主动改进了 In-app Log 的能力，缩短了问题排查的时间，提升了研发和 QA 的效率。通过增加日志文件解析能力并提供可视化查询页面，他显著提升了团队的工作效率，并优化了问题定位流程。特别是针对 QA 的需求，Nain 主动改善了日志的可视化展示，解决了过去依赖抓包工具的痛点。

启动性能优化
Nain 在 Branded App 启动性能优化方面，成功将应用的启动时间缩短了 60%以上，极大改善了用户体验。这项优化直接提升了商户的使用体验，并通过改造初始化流程和增加缓存机制，使得启动速度得到显著提升。

Mobile App 相关优化
Nain 在 Mobile App 的开发中也做出了显著贡献，特别是在 Image Crop Picker 功能和 App 灰度/回滚方案的实现上。他通过提供图片预处理能力，提升了商户在应用中的工作效率，同时通过引入版本控制和灰度发布方案，降低了发布过程中的风险，为团队带来了更高的发布安全性。

总体来说，Nain 在多个项目中展现了出色的技术能力和项目推动力，他的贡献不仅提升了团队的工作效率，也为公司的产品和服务质量带来了直接的改善。

### 问题 2

1. Be a Team Player
   Nain 在多个项目中展现了很强的团队合作精神。比如，在 In-app Message 的开发中，Nain 主动将 IM 功能封装成 SDK，提升了团队的可复用能力，并为团队的技术积累做出了贡献。尤其是在面对复杂的技术挑战时，他总是与团队密切协作，确保项目按时交付，并且帮助其他成员解决技术难题。

2. Focus on Customers
   在 Branded App 需求迭代中，Nain 深入了解商户的需求，通过提供定制化品牌应用方案，增加了商户的用户黏性和营收。在 In-app Message 功能的开发中，他考虑到商户沟通成本的问题，设计并实现了高效的消息传递方案，不仅为商户节省了费用，还提升了他们的用户体验，充分体现了“以客户为中心”的价值观。

3. Establish and Expand Ownership
   Nain 在多个项目中表现出了极强的责任感。在系统日志建设中，他不仅识别到当前流程中的痛点，还主动提出解决方案，并成功实现了日志系统的改进，极大地提高了团队的工作效率。在 Branded App 启动性能优化中，Nain 也表现出强烈的责任心，通过自己对性能瓶颈的深度分析，提出并实施了切实可行的解决方案。

4. Be Relentlessly Resourceful
   Nain 在面对技术难题时展现出了极强的应变能力。比如在 Mobile App Image Crop Picker 功能的开发中，他引入了新的 Native 能力来提升性能，并通过功能开关策略，解决了版本兼容性的问题。在 App 灰度/回滚方案的优化中，Nain 不仅提供了技术方案，还通过团队协作帮助整个团队有效降低了发布风险。

5. Raise the Bar
   Nain 通过持续优化产品性能和用户体验，推动团队向更高标准迈进。在 Branded App 启动性能优化中，Nain 通过技术改造显著提升了启动速度，提升了用户体验。这种对质量和性能的不断追求展现了他提升团队标准的决心。

6. SEE (Simple, Explicit, Effective)
   Nain 在多个项目中都非常注重技术实现的简洁性和有效性。例如，在 In-app Message 功能的开发中，他设计的 SDK 既具备高可扩展性，又简洁易用，确保了后续的快速迭代和移植。通过这种简洁有效的设计，他为团队的工作提供了更高效的技术支持。

### 问题 3

无
